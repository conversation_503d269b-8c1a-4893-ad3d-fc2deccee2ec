import { configureStore } from "@reduxjs/toolkit";
import miscReducer from "./misc/miscSlice";
import authReducer from "./auth/authSlice";
import branchesReducer from "./branches/branchesSlice";
import employeesReducer from "./employees/employeesSlice";
import alertsReducer from "./alerts/alertsSlice";
import dashboardReducer from "./dashboard/dashboardSlice";
import eventsReducer from "./events/eventsSlice";

export const store = configureStore({
  reducer: {
    misc: miscReducer,
    auth: authReducer,
    branches: branchesReducer,
    employees: employeesReducer,
    alerts: alertsReducer,
    dashboard: dashboardReducer,
    theftAlerts: eventsReducer,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
