import React from "react";
import ReactPlayer from "react-player";

interface IVideoPlayerProps {
  video_clip: string;
}

const VideoPlayer: React.FC<IVideoPlayerProps> = ({ video_clip }) => {
  return (
    <div>
      <ReactPlayer
        src={video_clip}
        controls={true} // Show default player controls
        playing={false} // Start playing automatically
        loop={false} // Loop the video
        muted={true} // Mute by default
        width="100%"
        height="100%"
        // Optional: Custom styling
        style={{
          maxWidth: "100%",
          maxHeight: "518px",
          margin: "0 auto",
          aspectRatio: "4/3", // Maintain aspect ratio
          borderRadius: "24px 24px 0 0 ",
          border: "1px solid #E8E8E8",
          borderBottom: "none",
        }}
        // Callback functions
        onReady={() => console.log("Player is ready")}
        onPlay={() => console.log("Video started playing")}
        onPause={() => console.log("Video paused")}
        onEnded={() => console.log("Video ended")}
        onError={(e) => console.error("Player error:", e)}
      />
    </div>
  );
};

export default VideoPlayer;
