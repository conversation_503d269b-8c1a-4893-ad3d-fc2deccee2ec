import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../components/api/api";

interface IEventsState {
  theftAlerts: ITheftAlertsData[];
  singleAlert: ITheftAlertsData;
  status: "idle" | "loading" | "succeeded" | "failed";
  error: string | null;
}

const initialState: IEventsState = {
  theftAlerts: [
    {
      id: 0,
      title: "Theft in Vake #3 branch",
      text: "Potential Theft by customer",

      camera: 1,
      timestamp: "22:05:52",
      avatarPath: null,
      branch: {
        name: "Vake #3",
        address: "Tbilisi, Chavchavadze #12",
        email: "<EMAIL>",
        phone: "************",
        fraud_risk: 3,
        coordinates: { longitude: "44.787197", latitude: "41.715137" },
        status: "Good",
        alerts: 12,
        revenue: 1560,
      },
      employee: null,
      confidence: 0.75,
      video_clip: "",
    },
  ],
  singleAlert: {
    id: 0,
    title: "Theft in Vake #3 branch",
    text: "Potential Theft by customer",

    camera: 0,
    timestamp: "22:05:52",
    avatarPath: null,
    branch: {
      name: "Vake #3",
      address: "Tbilisi, Chavchavadze #12",
      email: "<EMAIL>",
      phone: "************",
      fraud_risk: 3,
      coordinates: { longitude: "44.787197", latitude: "41.715137" },
      status: "Good",
      alerts: 12,
      revenue: 1560,
    },
    employee: null,
    confidence: 0.75,
    video_clip: "",
  },
  status: "idle",
  error: null,
};

// Create the async thunk
export const fetchTheftAlerts = createAsyncThunk(
  "theftAlerts/fetchEvents",
  async (filters: IEventsFilters | undefined, { rejectWithValue }) => {
    try {
      const response = await api.get("api/events", { params: filters });
      console.log(response.data);
      return response.data;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const fetchSingleTheftAlert = createAsyncThunk(
  "theftAlerts/fetchSingleEvents",
  async (id: string, { rejectWithValue }) => {
    try {
      const response = await api.get(`api/events/${id}`);
      console.log(response.data);
      return response.data;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

const eventsSlice = createSlice({
  name: "theftAlerts",
  initialState,
  reducers: {
    setEvents: (state, action) => {
      state.theftAlerts = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchTheftAlerts.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchTheftAlerts.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.theftAlerts = action.payload;
      })
      .addCase(fetchTheftAlerts.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload as string;
      })
      .addCase(fetchSingleTheftAlert.pending, (state) => {
        state.status = "loading";
      })
      .addCase(fetchSingleTheftAlert.fulfilled, (state, action) => {
        state.status = "succeeded";
        state.singleAlert = action.payload;
      })
      .addCase(fetchSingleTheftAlert.rejected, (state, action) => {
        state.status = "failed";
        state.error = action.payload as string;
      });
  },
});

export const { setEvents } = eventsSlice.actions;
export default eventsSlice.reducer;
