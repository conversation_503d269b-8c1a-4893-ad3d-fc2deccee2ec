import React, { useEffect, useMemo } from "react";
import { useSelector, useDispatch } from "react-redux";
import type { RootState, AppDispatch } from "../../redux/store";
import { fetchTheftAlerts } from "../../redux/events/eventsSlice";

import TheftCard from "../Shared/TheftCard";

const TheftAlerts: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const dispatch = useDispatch<AppDispatch>();
  const { theftAlerts } = useSelector((state: RootState) => state.theftAlerts);

  // Separate events into inHouse and customer arrays
  const { inHouse, customer } = useMemo(() => {
    console.log(theftAlerts);
    const inHouseEvents = theftAlerts?.filter(
      (event) => event.employee !== null // Events with employees are inHouse
    );
    const customerEvents = theftAlerts?.filter(
      (event) => event.employee === null // Events without employees are customer-related
    );

    return {
      inHouse: inHouseEvents,
      customer: customerEvents,
    };
  }, [theftAlerts]);

  useEffect(() => {
    dispatch(fetchTheftAlerts({ organization: "1", branch: "1" }));
  }, [dispatch]);

  console.log(theftAlerts);

  return (
    <section className="mt-[2rem] pb-[1.8rem] border-[1px] border-[#fff]">
      {children}
      <div className="flex flex-wrap gap-[2rem] mt-[1.5rem]">
        <div className="flex-1 flex flex-col gap-[1.3rem]">
          <h3 className="text-[1.4rem] font-medium text-[#000] ml-[1.6rem]">
            Inhouse
          </h3>
          <div className="flex flex-col gap-[1rem]">
            {inHouse.map((alert: ITheftAlertsData, index: number) => (
              <TheftCard key={index} {...alert} />
            ))}
          </div>
        </div>

        <div className="flex-1 flex flex-col gap-[1.3rem]">
          <h3 className="text-[1.4rem] font-medium text-[#000] ml-[1.6rem]">
            Customer
          </h3>
          <div className="flex flex-col gap-[1rem]">
            {customer.map((alert: ITheftAlertsData, index: number) => (
              <TheftCard key={index} {...alert} />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TheftAlerts;
