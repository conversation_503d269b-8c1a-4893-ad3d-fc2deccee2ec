import React, { useEffect } from "react";
import { useParams } from "react-router";
import VideoPlayer from "./VideoPlayer";
import { fetchSingleTheftAlert } from "../../redux/events/eventsSlice";
import { useSelector, useDispatch } from "react-redux";
import type { RootState, AppDispatch } from "../../redux/store";
//import TableHeader from "../Shared/TableHeader";

const SingleTheftAlert: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();

  const { id } = useParams();

  const { singleAlert } = useSelector((state: RootState) => state.theftAlerts);

  useEffect(() => {
    dispatch(fetchSingleTheftAlert(id as string));
  }, [dispatch, id]);

  return (
    <div className="pt-[2.4rem] max-w-[90.8rem] rounded-[24px] overflow-hidden">
      {/* <TableHeader
        title="Thefts"
        icon="/images/dashboard/incognito-gray.svg"
        className="rounded-[1.2rem]"
      /> */}
      <img src="" alt="" />
      <VideoPlayer video_clip={singleAlert.video_clip} />
      <div className="bg-[#fff] border-[1px] border-[#E8E8E8]">
        <div className="flex items-center  py-[.8rem] px-[1.6rem] border-b-[1px] border-b-[#E8E8E8]">
          <div className="flex items-center flex-1 gap-[.8rem]">
            <span className="text-[1.4rem] font-[500] leading-[2.8rem]">
              {singleAlert.timestamp}
            </span>
            {singleAlert.employee ? (
              <span className="text-[1.4rem] font-[500] leading-[2.8rem]">{`${singleAlert.employee?.first_name} ${singleAlert.employee?.last_name}`}</span>
            ) : (
              <span className="text-[1.4rem] font-[500] leading-[2.8rem]">
                Unkown Customer
              </span>
            )}
          </div>

          <div className="flex-1 flex items-center justify-between">
            <span>{singleAlert.branch.name}</span>
            <div className="flex items-center gap-[.8rem]">
              <span className="py-[.4rem] px-[.8rem] bg-[#FBE0E0] rounded-[1rem] text-[#7C1111] text-[1.2rem] font-[600] ">
                Suspected theft
              </span>
              <img src="/images/alert-arrow-diagonal.png" alt="" />
            </div>
          </div>
        </div>

        <div className="flex items-center gap-[2.4rem] px-[1.6rem] py-[.8rem]">
          <div className="flex items-center">
            <img src="/images/phone-icon.png" alt="" />
            <div className="flex flex-col">
              <span className="text-[#393939] text-[1.2rem] ">
                Branch Manager
              </span>
              <span className="text-[#000] text-[1.2rem] font-[500] ">
                +995 XXX XX XX XX
              </span>
            </div>
          </div>
          <div className="flex items-center">
            <img src="/images/phone-icon.png" alt="" />
            <div className="flex flex-col">
              <span className="text-[#393939] text-[1.2rem] ">
                Branch Security
              </span>
              <span className="text-[#000] text-[1.2rem] font-[500] ">
                +995 XXX XX XX XX
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
export default SingleTheftAlert;
