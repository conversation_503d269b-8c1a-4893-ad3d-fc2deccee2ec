import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import "./index.css";
import { createBrowserRouter, redirect, RouterProvider } from "react-router";
import MainLayout from "./layouts/MainLayout";
import Dashboard from "./pages/Dashboard";
import Branches from "./pages/Branches";
import Employees from "./pages/Employees";
import SingleEmployee from "./components/ForEmployees/SingleEmployee";
import Alerts from "./pages/Alerts";
import Thefts from "./pages/Thefts";

import { Provider } from "react-redux";
import { store } from "./redux/store";
import SingleTheftAlert from "./components/thefts/SingleTheftAlert";

const router = createBrowserRouter([
  {
    path: "/",
    loader: () => redirect("/dashboard"),
  },
  {
    path: "/dashboard",
    element: <MainLayout />,
    children: [
      {
        path: "",
        element: <Dashboard />,
      },
      {
        path: "branches",
        element: <Branches />,
      },
      {
        path: "employees",
        element: <Employees />,
      },
      {
        path: "employees/:id",
        element: <SingleEmployee />,
      },
      {
        path: "alerts",
        element: <Alerts />,
        children: [
          {
            path: "thefts",
            element: <Thefts />,
          },
          {
            path: "thefts/single-theft/:id",
            element: <SingleTheftAlert />,
          },
        ],
      },
    ],
  },
]);

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <Provider store={store}>
      <RouterProvider router={router} />
    </Provider>
  </StrictMode>
);
